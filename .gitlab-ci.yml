variables:
  TERM: xterm
  TECHNOLOGY: NONE

cache:
  key:
    files:
      - yarn.lock
  paths:
    - node_modules/
  policy: pull-push

stages:
  - build
  - test
  - deploy

build:remix:
  image: intractosre/node:20
  stage: build
  script:
    - yarn install --frozen-lockfile
    - yarn build
    - yarn prisma generate
  artifacts:
    paths:
      - app/dist
      - node_modules
      - build

test:typecheck:
  image: intractosre/node:20
  stage: test
  dependencies:
    - build:remix
  script:
    - yarn typecheck

test:remix:
  image: mcr.microsoft.com/playwright:v1.51.0-noble
  stage: test
  parallel: 7
  dependencies:
    - build:remix
  services:
    - name: bitnami/mysql:8.0
      alias: mysql
  variables:
    MYSQL_USER: deme-digital-simops
    MYSQL_PASSWORD: deme-digital-simops
    MYSQL_DATABASE: deme-digital-simops
    ALLOW_EMPTY_PASSWORD: yes
    DATABASE_URL: mysql://deme-digital-simops:deme-digital-simops@mysql:3306/deme-digital-simops
    DEBIAN_FRONTEND: noninteractive
    COOKIE_SECRET: "#hy349;8t5gk#$thti;avfkwsm"
    TEST_MODE: 1
    CONFIG_PATH: './config/DEV/'
  script:
    - apt-get update -qq
    - apt-get install -y ghostscript graphicsmagick
    - yarn prisma migrate reset -f
    - TEST_MODE=1 npx playwright test --shard=$CI_NODE_INDEX/$CI_NODE_TOTAL
  artifacts:
    when: always
    paths:
      - test-results/
      - playwright-report/
      - results.xml
    reports:
      junit: results.xml
    expire_in: 1 week

deploy:website-testing:
  only:
    - testing
  image: intractosre/deploy:1
  stage: deploy
  environment: testing
  before_script:
    - if [ -f /itr-init.sh ]; then source /itr-init.sh; fi
  variables:
    itr_server_environment: test
    itr_server_host: deme.testing.intracto.com
    itr_server_user: deme
    itr_server_base: /var/www/test.deme.hosted-temp.com
    itr_server_current: ${itr_server_base}/current
    itr_server_next: ${itr_server_base}/next
    itr_server_shared: ${itr_server_base}/shared
    itr_server_releases: ${itr_server_base}/releases
    itr_server_url: https://test.deme.hosted-temp.com
    itr_opcache_reset: 0
    DATABASE_URL: mysql://deme-digital-simops:deme-digital-simops@mysql:3306/deme-digital-simops
  dependencies:
    - build:remix
  script:
    #   Clone and init itr_deploy_scripts
    - ssh-keyscan -t rsa gitlab.hosted-tools.com >> ~/.ssh/known_hosts
    - if [ ! -d .itr/scripts ]; then <NAME_EMAIL>:intracto/itr_deploy_scripts .itr/scripts; rm -rf .itr/scripts/.git; fi
    - cd .itr/scripts
    - ./deploy.sh general env

    #   Rsync files to server
    - ssh-keyscan -t rsa ${itr_server_host} >> ~/.ssh/known_hosts
    - rsync -azc --delete --rsh='ssh -p 22' ${CI_PROJECT_DIR}/ ${itr_server_user}@${itr_server_host}:${itr_server_shared}/cached-copy/

    #   Prepare release
    - ssh ${itr_server_user}@${itr_server_host} ${itr_server_shared}/cached-copy/.itr/scripts/deploy.sh general prepare_release

    #   Run main framework task
    - ssh ${itr_server_user}@${itr_server_host} ${itr_server_shared}/cached-copy/.itr/scripts/deploy.sh none main

    # Run post deploy docker build
    - ssh ${itr_server_user}@${itr_server_host} docker build --target build -t deme-digital-simops ${itr_server_shared}/cached-copy/

    # Deploy docker containers
    - ssh ${itr_server_user}@${itr_server_host} ${itr_server_shared}/cached-copy/.itr/overrides/scripts/deploy.sh ${itr_server_shared} ${DATABASE_URL}
