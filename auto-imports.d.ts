/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const IconCarbonJumpLink: typeof import('~icons/carbon/jump-link.jsx')['default']
  const IconFa6SolidHelmetSafety: typeof import('~icons/fa6-solid/helmet-safety.jsx')['default']
  const IconJamRefresh: typeof import('~icons/jam/refresh.jsx')['default']
  const IconLogosMicrosoftIcon: typeof import('~icons/logos/microsoft-icon.jsx')['default']
  const IconMaterialSymbolsAddCommentOutline: typeof import('~icons/material-symbols/add-comment-outline.jsx')['default']
  const IconMaterialSymbolsArrowDropDownRounded: typeof import('~icons/material-symbols/arrow-drop-down-rounded.jsx')['default']
  const IconMaterialSymbolsArrowRightAltRounded: typeof import('~icons/material-symbols/arrow-right-alt-rounded.jsx')['default']
  const IconMaterialSymbolsCheckSmall: typeof import('~icons/material-symbols/check-small.jsx')['default']
  const IconMaterialSymbolsChevronRight: typeof import('~icons/material-symbols/chevron-right.jsx')['default']
  const IconMaterialSymbolsCloseRounded: typeof import('~icons/material-symbols/close-rounded.jsx')['default']
  const IconMaterialSymbolsCloseSmall: typeof import('~icons/material-symbols/close-small.jsx')['default']
  const IconMaterialSymbolsCodeRounded: typeof import('~icons/material-symbols/code-rounded.jsx')['default']
  const IconMaterialSymbolsDeleteOutline: typeof import('~icons/material-symbols/delete-outline.jsx')['default']
  const IconMaterialSymbolsDeleteOutlineRounded: typeof import('~icons/material-symbols/delete-outline-rounded.jsx')['default']
  const IconMaterialSymbolsEditRounded: typeof import('~icons/material-symbols/edit-rounded.jsx')['default']
  const IconMaterialSymbolsExpand: typeof import('~icons/material-symbols/expand.jsx')['default']
  const IconMaterialSymbolsFileCopyRounded: typeof import('~icons/material-symbols/file-copy-rounded.jsx')['default']
  const IconMaterialSymbolsKeyboardArrowDown: typeof import('~icons/material-symbols/keyboard-arrow-down.jsx')['default']
  const IconMaterialSymbolsLeftPanelOpenOutline: typeof import('~icons/material-symbols/left-panel-open-outline.jsx')['default']
  const IconMaterialSymbolsLogoutRounded: typeof import('~icons/material-symbols/logout-rounded.jsx')['default']
  const IconMaterialSymbolsMenu: typeof import('~icons/material-symbols/menu.jsx')['default']
  const IconMaterialSymbolsModeCommentOutline: typeof import('~icons/material-symbols/mode-comment-outline.jsx')['default']
  const IconMaterialSymbolsMoreVert: typeof import('~icons/material-symbols/more-vert.jsx')['default']
  const IconMaterialSymbolsNightlightOutline: typeof import('~icons/material-symbols/nightlight-outline.jsx')['default']
  const IconMaterialSymbolsPause: typeof import('~icons/material-symbols/pause.jsx')['default']
  const IconMaterialSymbolsPerson2OutlineRounded: typeof import('~icons/material-symbols/person2-outline-rounded.jsx')['default']
  const IconMaterialSymbolsPlayArrow: typeof import('~icons/material-symbols/play-arrow.jsx')['default']
  const IconMaterialSymbolsSunnyOutlineRounded: typeof import('~icons/material-symbols/sunny-outline-rounded.jsx')['default']
  const IconMaterialSymbolsVisibility: typeof import('~icons/material-symbols/visibility.jsx')['default']
  const IconMdiAlertOutline: typeof import('~icons/mdi/alert-outline.jsx')['default']
  const IconMdiCalendarStart: typeof import('~icons/mdi/calendar-start.jsx')['default']
  const IconMdiChevronDownCircleOutline: typeof import('~icons/mdi/chevron-down-circle-outline.jsx')['default']
  const IconMdiChevronLeft: typeof import('~icons/mdi/chevron-left.jsx')['default']
  const IconMdiInformationVariantCircleOutline: typeof import('~icons/mdi/information-variant-circle-outline.jsx')['default']
  const IconMdiReload: typeof import('~icons/mdi/reload.jsx')['default']
  const IconMdiStop: typeof import('~icons/mdi/stop.jsx')['default']
  const IconTablerStatusChange: typeof import('~icons/tabler/status-change.jsx')['default']
}
