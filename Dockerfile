# syntax=docker/dockerfile:1
ARG PROJECT_DIR=/app/simops

# build image
FROM node:20-alpine AS build
ARG PROJECT_DIR
RUN apk add --no-cache openssl ghostscript graphicsmagick
WORKDIR $PROJECT_DIR

COPY . $PROJECT_DIR

ENV COOKIE_SECRET='#hy349;8t5gk#\$thti;avfkwsm'

ENV PROCEDURE_PATH='/procedures'
VOLUME $PROCEDURE_PATH

ENV FILE_UPLOAD_DIR='/uploads'
ENV RESOURCES_DIR='/resources'
RUN mkdir -p $FILE_UPLOAD_DIR
RUN mkdir -p $RESOURCES_DIR
VOLUME $FILE_UPLOAD_DIR
VOLUME $RESOURCES_DIR


RUN yarn install --frozen-lockfile && yarn build

EXPOSE 3000
RUN chmod +x ./app/entrypoint.sh
ENTRYPOINT [ "./app/entrypoint.sh" ]

# production image
FROM node:20-alpine AS production
ARG PROJECT_DIR
ENV NODE_ENV=production
RUN apk add --no-cache openssl ghostscript graphicsmagick
WORKDIR $PROJECT_DIR

COPY --from=build ${PROJECT_DIR}/build ./build
COPY --from=build ${PROJECT_DIR}/app/entrypoint.sh ./app/
COPY --from=build ${PROJECT_DIR}/package.json .
COPY --from=build ${PROJECT_DIR}/yarn.lock .
COPY --from=build ${PROJECT_DIR}/prisma ./prisma
COPY --from=build ${PROJECT_DIR}/server.mjs .

RUN yarn install --frozen-lockfile --production && yarn cache clean

EXPOSE 3000
ENTRYPOINT [ "./app/entrypoint.sh" ]
