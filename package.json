{"private": true, "sideEffects": false, "type": "module", "scripts": {"test:all": "dotenv -e .env.local -- npx playwright test", "test:gui": "dotenv -e .env.local -- npx playwright test --ui", "test:generate": "yarn test:dev & npx playwright codegen localhost:3030", "test:dev": "cross-env SHOW_TOGGLE_ALL=true TEST_MODE=1 PORT=3030 PROCEDURE_PATH=./tests/fixtures/procedures/ node ./server.mjs", "test:start-server": "yarn build && cross-env PORT=3030 CONFIG_PATH=./config/DEV/ SHOW_TOGGLE_ALL=true TEST_MODE=1  PROCEDURE_PATH=./tests/fixtures/procedures/ NODE_ENV=production node server.mjs > playwright.log", "script:updateStepIndex": "node ./scripts/updateStepIndex.mjs", "script:seedDatabase": "node ./scripts/seedDatabase.mjs", "build": "react-router typegen && react-router build", "dev": "node ./server.mjs", "prisma:reset": "dotenv -e .env.local -- prisma migrate reset -f", "typecheck": "react-router typegen && tsc", "whitelist:reset": "cp config/DEV/email-whitelist.json.dist config/DEV/email-whitelist.json", "start": "cross-env NODE_ENV=production node ./server.mjs"}, "dependencies": {"@epic-web/remember": "^1.1.0", "@iconify-json/carbon": "^1.2.8", "@iconify-json/logos": "^1.1.43", "@mjackson/form-data-parser": "^0.7.0", "@prisma/client": "^6.4.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-icons": "^1.2.0", "@radix-ui/react-toast": "^1.2.6", "@react-hookz/web": "^25.1.0", "@react-router/express": "^7.3.0", "@react-router/fs-routes": "^7.3.0", "@react-router/node": "^7.3.0", "@svgr/core": "^8.1.0", "@svgr/plugin-jsx": "^8.1.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.13", "@tailwindcss/vite": "^4.0.12", "@types/lodash-es": "^4.17.12", "@types/mime": "^4.0.0", "@types/node": "^22.13.9", "@use-gesture/react": "^10.3.1", "add": "^2.0.6", "chalk": "^5.0.0", "clsx": "^2.1.1", "compression": "^1.8.0", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "express": "^4.18.2", "isbot": "^5.1.23", "lodash-es": "^4.17.21", "lucide-react": "^0.479.0", "mime": "^4.0.3", "morgan": "^1.10.0", "motion": "^12.5.0", "pdf-lib": "^1.17.1", "pdf2pic": "^3.1.4", "prisma": "^6.4.1", "react": "^19.0.0", "react-aria-components": "^1.7.0", "react-dom": "^19.0.0", "react-focus-lock": "^2.13.6", "react-markdown": "^10.1.0", "react-router": "^7.3.0", "react-scan": "^0.2.14", "remix-auth": "^4.1.0", "remix-auth-form": "^3.0.0", "remix-auth-oauth2": "^3.3.0", "remix-utils": "^8.5.0", "tailwind-merge": "^3.0.2", "tailwind-variants": "^0.3.1", "tailwindcss": "^4.0.12", "tiny-invariant": "^1.3.3", "ts-node": "^10.9.1", "unstorage": "^1.15.0", "url-slug": "^4.0.1", "uuid": "^11.1.0", "vite": "^6.2.1", "yn": "^5.0.0", "yup": "^1.0.0-beta.8"}, "devDependencies": {"@iconify-json/fa6-solid": "^1.1.20", "@iconify-json/jam": "^1.1.7", "@iconify-json/material-symbols": "^1.2.15", "@iconify-json/mdi": "^1.2.3", "@iconify-json/svg-spinners": "^1.2.2", "@iconify-json/tabler": "^1.2.17", "@playwright/test": "^1.51.0", "@react-router/dev": "^7.3.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/uuid": "^10.0.0", "dotenv": "^16.0.3", "nodemon": "^3.1.4", "npm-run-all": "^4.1.5", "npm-watch": "^0.13.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.2", "unplugin-auto-import": "^19.1.1", "unplugin-icons": "^22.1.0", "vite-env-only": "^3.0.3", "vite-tsconfig-paths": "^5.1.4", "yarn": "^1.22.21"}, "engines": {"node": ">=18"}}