import { type Page } from "playwright-core";
import { exec } from "child_process";
import { expect } from "@playwright/test";

function execShellCommand(cmd: string) {
  return new Promise((resolve, reject) => {
    exec(cmd, (error: Error | null, stdout: string, stderr: string) => {
      if (error) {
        console.warn(error);
      }
      resolve(stdout ? stdout : stderr);
    });
  });
}

export const delay = (time: number) =>
  new Promise((resolve) => setTimeout(resolve, time));

export async function enterName(page: Page, name: string = "test-operator") {
  await login(page, { displayName: name });
}

export async function login(
  page: Page,
  user: { displayName: string; email?: string },
) {
  await page.goto("/login/dummy-login");
  await page.getByPlaceholder("<EMAIL>").click();
  await page
    .getByPlaceholder("<EMAIL>")
    .fill(user.email || "<EMAIL>");
  await page.getByPlaceholder("test operator").click();
  await page.getByPlaceholder("test operator").fill(user.displayName);
  await page.getByRole("button", { name: "Login" }).click();
  await expectText(page, user.displayName);
}

export async function logout(page: Page) {
  await page.goto("/logout");
  const url = page.url();
  expect(url).toContain("/login");
}

export async function reset(page: Page) {
  await delay(1000);
  await execShellCommand("yarn prisma migrate reset -f");
  await page.goto("/clear-cache");
}

export async function createProcedure(
  page: Page,
  name: string,
  context?: string,
) {
  await page.goto("/");
  await page.getByRole("link", { name: "Start a procedure" }).click();
  await expectPageTitle(page, "Start a procedure");
  await page.getByRole("button", { name: name }).click();

  if (context) {
    await page.getByRole("button", { name: "Select an item" }).click();
    await page.getByRole("option", { name: context }).click();
    await page.getByRole("button", { name: "Start procedure" }).click();
  }

  await expectTextByTestId(page, "page-title", name);
}

export async function switchRole(page: Page, fromRole: string, toRole: string) {
  await page.getByRole("link", { name: fromRole, exact: true }).click();
  await expectPageTitle(page, "Choose your role");
  await page.getByRole("button", { name: `Role ${toRole}` }).click();
  await expectRole(page, toRole);
}

export async function selectRole(page: Page, role: string) {
  await page.getByRole("button", { name: `Role ${role}` }).click();
  await expectRole(page, role);
}

export async function expectRole(page: Page, role: string) {
  await expectTextByTestId(page, "role-link", role);
}

export async function selectActiveProcedure(
  page: Page,
  name: string,
  pageTitle: string | null = null,
) {
  if (!pageTitle) {
    pageTitle = name;
  }

  await page.goto("/");
  await page.getByRole("button", { name: name }).click();
  await expectTextByTestId(page, "page-title", pageTitle);
}

export async function forceNextStep(page: Page) {
  await expectText(page, "Force next step");
  await page.getByTestId("next").click({ force: true });
  await expectText(
    page,
    "The supervisor forced the procedure to the next step",
  );
}

export async function forcePreviousStep(page: Page) {
  await expectText(page, "Force previous step");
  await page.getByTestId("prev").click({ force: true });
  await expectText(
    page,
    "The supervisor forced the procedure to the previous step",
  );
}

export async function expectPageTitle(page: Page, title: string) {
  await expectTextByTestId(page, "page-title", title);
}

export async function expectText(page: Page, text: string) {
  await expect(page.getByText(text)).toBeVisible();
}
export async function doNotExpectText(page: Page, text: string) {
  await expect(page.getByText(text)).toBeVisible({ visible: false });
}

export async function checkTask(page: Page, label: string) {
  await page.getByTestId("task-checkbox").getByText(label).click();
  await expectCheckboxIsChecked(page, label);
}

export async function expectCheckboxIsChecked(page: Page, label: string) {
  await expect(page.getByLabel(label)).toBeChecked();
}

export async function expectCheckboxIsNotChecked(page: Page, label: string) {
  await expect(page.getByLabel(label)).not.toBeChecked();
}

export async function markAllSubtasksAsDone(page: Page) {
  await page.getByRole("button", { name: "Mark all subtasks as done" }).click();
  await expect(
    page.getByRole("button", { name: "Mark all subtasks as undone" }),
  ).toBeVisible();
}

async function expectTextByTestId(page: Page, testId: string, text: string) {
  await expect(page.getByTestId(testId).getByText(text)).toBeVisible();
}
