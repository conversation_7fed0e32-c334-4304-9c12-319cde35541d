import * as DeprecatedToast from "@radix-ui/react-toast";
import {
  data,
  <PERSON><PERSON>,
  <PERSON>a,
  <PERSON>let,
  <PERSON><PERSON><PERSON>,
  useMatch<PERSON>,
  useNavigate,
  useNavigation,
} from "react-router";
import React, { type ReactNode, useEffect, useState } from "react";
import "~/styles/app.css";

import { AnimatePresence, motion } from "motion/react";
import MessageDialog from "~/modules/messages/message-dialog";
import { messagesForRequest } from "~/modules/messages/server";
import { getName } from "~/modules/operator/name.server";
import { getSession, sessionMiddleware } from "./modules/session/index.server";
import { getRole } from "~/modules/operator/role.server";
import { RenderContextProvider } from "~/modules/helpers/render-context-provider";
import { parseColorScheme } from "~/modules/color-scheme/server";
import { getSelectedProcedureSlug } from "~/modules/procedure/active-procedure.server";
import {
  ColorSchemeScript,
  useIsSystemDarkMode,
} from "~/modules/color-scheme/components";
import { cn } from "~/utils";

import { useConnectionStatus } from "~/modules/connection/use-connection-status";
import { usePollingRoute } from "~/modules/hooks/use-route-polling";
import { type Message, type Notification } from "~/modules/database/types";
import { type OperatorRole } from "~/modules/operator/types";
import { type User } from "~/modules/user/types";
import { RouterProvider } from "@react-aria/utils";
import { type Route } from "../.react-router/types/app/+types/root";
import { Header } from "~/components/ui/header";
import type { EventSourceMap } from "remix-utils/sse/react";
import { scan } from "react-scan";
import { getUser } from "~/modules/auth/authentication.server";
import { contextStorageMiddleware } from "~/modules/context.middleware.server";
import { getEnvConfig } from "./utils/env.utils";

export function meta() {
  return [
    { charset: "utf-8" },
    { name: "viewport", content: "width=device-width,initial-scale=1" },
    { name: "title", content: "SIMOPS" },
  ];
}

interface Env {
  reactScan: boolean;
  showToggleAll: boolean;
  polling: boolean;
  pollingInterval: number;
  forceSupervisorPanelsExpanded: boolean;
}

export interface RootLoaderData {
  env: Env;
  colorScheme: any;
  messages: Message[];
  notifications: Notification[];
  operatorName: string | null;
  role: OperatorRole | null;
  headerLink: string;
  procedureSlug: string;
  user: User | null;
}

export const unstable_middleware = [
  sessionMiddleware,
  contextStorageMiddleware,
];

export const loader = async (loaderArgs: Route.LoaderArgs) => {
  const user = await getUser();
  const session = getSession();
  loaderArgs.context;

  const [colorScheme, { messages, notifications }, operatorName] =
    await Promise.all([
      parseColorScheme(),
      messagesForRequest(loaderArgs),
      getName(),
    ]);

  let procedureSlug =
    loaderArgs.params?.procedure ?? getSelectedProcedureSlug();
  if (
    loaderArgs.params?.procedure &&
    procedureSlug !== loaderArgs.params?.procedure
  ) {
    procedureSlug = loaderArgs.params.procedure;
    session.set("selected-procedure", procedureSlug);
  }

  const role = await getRole().catch(() => null);

  const emptyLoaderData: RootLoaderData = {
    env: getEnvConfig(process.env),
    colorScheme,
    messages,
    notifications,
    operatorName,
    role,
    headerLink: "/",
    procedureSlug: "",
    user,
  };

  try {
    let headerLink = "/";
    if (role) {
      headerLink = `/${procedureSlug}/1/1`;

      if (role.slug === "supervisor")
        headerLink = `/${procedureSlug}/supervisor`;
      if (role.slug === "viewer") headerLink = `/${procedureSlug}/viewer`;
    }

    return data({
      env: getEnvConfig(process.env),
      colorScheme,
      messages,
      notifications,
      operatorName,
      role,
      headerLink,
      procedureSlug,
      user,
    });
  } catch (error) {
    return data(emptyLoaderData);
  }
};

export function Layout({ children }: { children: ReactNode }) {
  let isDarkMode = useIsSystemDarkMode();
  let map: EventSourceMap = new Map();
  let matches = useMatches();
  const { hasConnection } = useConnectionStatus();
  const navigate = useNavigate();

  // temp fix to force revalidation
  usePollingRoute();

  const allowScrolling = Boolean(
    matches.find((match: any) => match?.handle?.allowScrolling),
  );
  const isFullPage = Boolean(
    matches.find((match: any) => match?.handle?.isFullPage),
  );
  const noHeader = Boolean(
    matches.find((match: any) => match?.handle?.noHeader),
  );
  const bodyClassNames = matches.map(
    (match: any) => match.handle?.bodyClassName,
  );

  return (
    <html
      lang="en"
      style={{ "--header-height": "73px" } as React.CSSProperties}
      className={cn(
        `scroll h-full`,
        { dark: isDarkMode },
        { "rounded-sm border-4 border-red-500": !hasConnection },
        { "md:overflow-hidden": !allowScrolling },
      )}
      suppressHydrationWarning
    >
      <head>
        <Meta />
        <Links />
        <link rel="manifest" href="/manifest.webmanifest" />
      </head>
      <body
        className={cn(
          "bg-neutral dark:bg-dark-blue-600 dark:text-neutral flex min-h-svh flex-col",
          { "pb-safe-area": !isFullPage },
          bodyClassNames,
        )}
      >
        <ColorSchemeScript />
        <RouterProvider navigate={navigate}>
          <>
            {!noHeader ? <Header /> : null}
            <div
              className={cn(
                "flex grow flex-col",
                {
                  "md:overflow-hidden": !allowScrolling,
                },
                {
                  "pt-3 md:pt-6": !isFullPage,
                },
              )}
            >
              {children}
            </div>
          </>
        </RouterProvider>
        <Scripts />
      </body>
    </html>
  );
}

export { ReusableErrorBoundary as ErrorBoundary } from "~/modules/error/error-boundary";

export default function App({ loaderData }: Route.ComponentProps) {
  const [removedMessageUUIDs, setRemovedMessageUUIDs] = useState<string[]>([]);
  const messages = loaderData.messages;
  const navigation = useNavigation();

  // Optimistically remove messages and persist the removed UUIDs to prevent
  // them from showing again if a loader is revalidated before the action is processed
  useEffect(() => {
    const message = JSON.parse(
      String(navigation.formData?.get("message") || "{}"),
    );
    if (message.uuid) {
      setRemovedMessageUUIDs((prevUUIDs) => [...prevUUIDs, message.uuid]);
    }
  }, [navigation.formData]);

  // Start react-scan
  useEffect(() => {
    scan({
      enabled: loaderData.env.reactScan,
    });
  }, []);

  const optimisticMessages = messages.filter(
    (message) => !removedMessageUUIDs.includes(message.uuid),
  );

  return (
    <DeprecatedToast.Provider>
      <RenderContextProvider>
        <Outlet />
      </RenderContextProvider>
      <AnimatePresence>
        {optimisticMessages.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{
              opacity: 1,
            }}
            exit={{ opacity: 0 }}
          >
            <MessageDialog messages={optimisticMessages} />
          </motion.div>
        ) : null}
      </AnimatePresence>
    </DeprecatedToast.Provider>
  );
}
