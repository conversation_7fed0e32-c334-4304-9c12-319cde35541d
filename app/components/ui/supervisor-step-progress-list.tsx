import clsx from "clsx";
import { type Step } from "~/modules/database/types";
import { type OperatorRole } from "~/modules/operator/types";
import { SuperVisorTaskProgressCard } from "~/components/ui/supervisor-step-progress-card";
import { useEffect, useState } from "react";
import useBetterMediaQuery from "~/modules/hooks/use-media-query";
import useEnv from "~/modules/hooks/use-env";

export function SupervisorStepProgressList({
  className,
  roles,
  stepsForEachRole,
  currentStep,
  allowExpand = true,
}: {
  className?: string;
  roles: OperatorRole[];
  stepsForEachRole: Record<string, Step>;
  currentStep: Step;
  allowExpand?: boolean;
}) {
  const env = useEnv();
  const isMobile = !useBetterMediaQuery("(min-width: 768px)");
  const canExpandMultiple = isMobile;

  // If environment variable is set to force expansion, expand all roles
  const initialExpandedRoles = env.forceSupervisorPanelsExpanded
    ? roles.map(role => role.slug)
    : [];

  const [expandedRoles, setExpandedRoles] = useState<OperatorRole["slug"][]>(
    initialExpandedRoles,
  );

  const expandRole = (roleSlug: OperatorRole["slug"]) => {
    if (!allowExpand) return;

    // If forced expansion is enabled, don't allow collapsing
    if (env.forceSupervisorPanelsExpanded) return;

    if (expandedRoles.includes(roleSlug)) {
      if (canExpandMultiple)
        setExpandedRoles(expandedRoles.filter((r) => r !== roleSlug));
      else setExpandedRoles([]);
    } else if (canExpandMultiple)
      setExpandedRoles([...expandedRoles, roleSlug]);
    else setExpandedRoles([roleSlug]);
  };

  const style = (role: OperatorRole) => {
    if (expandedRoles.includes(role.slug)) {
      return { width: "100%" };
    } else if (expandedRoles.length > 0) {
      return { width: "100px" };
    }

    return { width: `${100 / roles.length}%` };
  };

  // if currentStepId differs from the last one, reset the expanded roles
  useEffect(() => {
    if (env.forceSupervisorPanelsExpanded) {
      setExpandedRoles(roles.map(role => role.slug));
    } else {
      setExpandedRoles([]);
    }
  }, [currentStep.id, env.forceSupervisorPanelsExpanded, roles]);

  return (
    <div
      className={clsx(
        ["flex grow flex-col gap-2 overflow-hidden md:flex-row"],
        { "md:gap-2": !expandedRoles },
        { "md:gap-1": expandedRoles },
      )}
    >
      {roles.map((role) => (
        <SuperVisorTaskProgressCard
          allowExpand={allowExpand}
          expandedRoles={expandedRoles}
          expandRole={expandRole}
          key={role.slug}
          step={stepsForEachRole[role.slug]}
          role={role}
          style={style(role)}
          className={clsx(["overflow-hidden max-md:w-full! md:grow"])}
        />
      ))}
    </div>
  );
}
